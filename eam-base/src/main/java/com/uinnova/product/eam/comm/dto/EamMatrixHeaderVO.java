package com.uinnova.product.eam.comm.dto;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * 矩阵表头VO
 * <AUTHOR>
 */
@Data
public class EamMatrixHeaderVO {

    @Comment("分类id")
    private Long classId;

    @Comment("字段id")
    private Long attrDefId;

    @Comment("字段名称")
    private String name;

    @Comment("是否主键：1是,0否")
    private Integer isMajor = 0;

    @Comment("是否是关系")
    private Boolean isRlt = false;

    @Comment("属性类型[PRO_TYPE]    属性类型:1=整数 2=小数 3=短文本(<=200) 4=长文本(<=1000) 5=文章 6=枚举 7=日期 8=字典 9=3D模型 10=图片 11=关联属性 12=文档 150=编码类型")
    private Integer proType;

    @Comment("是否必填[IS_REQUIRED]    是否必填:0=否，1=是")
    private Integer isRequired = 0;

    @Comment("缺省值[DEF_VAL]")
    private String defVal;

    @Comment("外部属性[关联资产18/关联属性11/数据字典8字段用值]")
    private String proDropSourceDef;

    @Comment("约束规则[计算16]")
    private String constraintRule;
}

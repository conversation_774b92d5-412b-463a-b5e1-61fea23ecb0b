package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
@Comment("最近常看表[UINO_EAM_RECENTLY_VIEW]")
public class EamRecentlyView implements EntityBean {

    private Long id;

    private Long createTime;

    private Long modifyTime;
    /** 观看的视图id */
    private Long diagramId;
    @Comment("/观看的方案的id")
    private Long planId;
    @Comment("矩阵id")
    private Long matrixId;
    /** 观看的架构类型，1：企业级架构资产，2：系统级架构资产 */
    @Deprecated
    private Integer viewBuild;
    /** 用户id */
    private Long userId;
    /** 特殊视图 0：否  1:是 */
    private Integer specialView;
    //TODO 确认矩阵是否是3
    @Comment("1视图，2表示的是查看的方案, 3表示是矩阵")
    private Integer viewType;

    @Comment("1.表示架构设计  2.表示架构资产  3.表示与我协作")
    private Integer viewArchitectureType;

    @Comment("如果查看的架构设计的话需要表示查看的是哪个架构设计的")
    private Integer dirType;
}

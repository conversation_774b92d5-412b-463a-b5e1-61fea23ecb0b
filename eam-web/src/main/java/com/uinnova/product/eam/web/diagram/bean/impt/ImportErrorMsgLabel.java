package com.uinnova.product.eam.web.diagram.bean.impt;

import com.binary.core.i18n.Language;

public class ImportErrorMsgLabel {
	private boolean isEnEnv = true;
	/**
	 * 默认的使用英文作为导出对象
	 */
	public ImportErrorMsgLabel() {
	}

	public ImportErrorMsgLabel(boolean useUserLanguage) {
		if (useUserLanguage) {
			try {
				Language language = Language.ZHC;
				if (!Language.EN.equals(language)) isEnEnv = false;
			} catch (Exception e) {
				//没有就认为是中文环境
			}
		}
	}
	public String getFailed() { return isEnEnv ? "failed" : "失败"; }
	public String getSuccessful() { return isEnEnv ? "successful" : "成功"; }
	public String getRequiredFieldMissing() { return isEnEnv ? "required field missing" : "必填项缺失"; }
	public String getLongLength() { return isEnEnv ? "long length" : "长度过长"; }
	public String getTypeError() { return isEnEnv ? "type error" : "类型错误"; }
	public String getPrimaryKeyRepeat() { return isEnEnv ? "primary key repeat" : "主键重复"; }
	public String getDuplicateValue() { return isEnEnv ? "duplicate value" : "值重复"; }
	public String getAlreadyExists() { return isEnEnv ? "already exists" : "已经存在"; }
	public String getDoesNotExists() { return isEnEnv ? "does not exists" : "不存在"; }
	public String getCiCodeRepeat() { return isEnEnv ? "the CiCode already exists in other categories" : "其他分类中已存在该CiCode"; }

	public String getCiDataImportType() { return isEnEnv ? "ciDataImport" : "CI数据"; }
	public String getRltDataImportType() { return isEnEnv ? "rltDataImport" : "关系数据"; }
}

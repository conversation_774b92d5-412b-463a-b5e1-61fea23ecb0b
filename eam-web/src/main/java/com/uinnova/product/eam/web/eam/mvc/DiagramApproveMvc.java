package com.uinnova.product.eam.web.eam.mvc;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.model.es.DiagramApproveRlt;
import com.uinnova.product.eam.service.DiagramProcessSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 *  视图审批
 */
@RestController
@RequestMapping("/eam/approve")
public class DiagramApproveMvc {

    @Resource
    DiagramProcessSvc diagramProcessSvc;

    @GetMapping("/queryApprovePath")
    @ModDesc(desc = "回显审批视图的位置", pDesc = "businessKey", rDesc = "位置信息", rType = RemoteResult.class)
    public RemoteResult queryApprovePath(@RequestParam String businessKey, @RequestParam String processDefinitionKey) {
        Map<String, Object> result = diagramProcessSvc.queryApprovePath(businessKey, processDefinitionKey);
        return new RemoteResult(result);
    }

    @PostMapping("/pushApproveAsset")
    @ModDesc(desc = "资产提交审批", pDesc = "businessKey", rDesc = "发布结果", rType = RemoteResult.class)
    public RemoteResult pushApproveAsset(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        String businessKey = jsonObject.getString("businessKey");
        String processDefinitionKey = jsonObject.getString("processDefinitionKey");
        Long dirId = jsonObject.getLong("dirId");
        String desc = jsonObject.getString("desc");
        String taskId = jsonObject.getString("taskId");
        diagramProcessSvc.pushApproveAsset(businessKey, processDefinitionKey, dirId, desc, taskId);
        return new RemoteResult("1");
    }

    @GetMapping("/queryProcessRltByPlanOrModelKey")
    @ModDesc(desc = "根据方案ID获取子流程关联数据（后门）", pDesc = "planId", rDesc = "审批关联数据", rType = RemoteResult.class)
    public RemoteResult queryProcessRltByPlanOrModelKey(@RequestParam String planKey, @RequestParam String modelKey, @RequestParam Long rootDirId) {
        List<DiagramApproveRlt> rlt = diagramProcessSvc.queryProcessRltByPlanOrModelKey(planKey, modelKey, rootDirId);
        return new RemoteResult(rlt);
    }

    @GetMapping("/queryProcessRltByRootDiagramId")
    @ModDesc(desc = "根据审批视图查询流程关联信息（后门）", pDesc = "diagramId", rDesc = "审批关联数据", rType = RemoteResult.class)
    public RemoteResult queryProcessRltByRootDiagramId(@RequestParam String diagramId, @RequestParam String ownerCode) {
        List<DiagramApproveRlt> rlt = diagramProcessSvc.queryProcessRltByRootDiagramId(diagramId, ownerCode);
        return new RemoteResult(rlt);
    }

    @PostMapping("/manualChangeRltAndDiagramStatusInfoByKeys")
    @ModDesc(desc = "手动调整审批关联表数据及视图状态（后门）", pDesc = "businessKeys", rDesc = "修改结果", rType = RemoteResult.class)
    public RemoteResult manualChangeRltAndDiagramStatusInfoByKeys(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        List<Long> businessKeys = jsonObject.getObject("businessKeys", List.class);
        Integer flowStatus = jsonObject.getInteger("flowStatus");
        Boolean flag = diagramProcessSvc.manualChangeRltAndDiagramStatusInfoByKeys(businessKeys, flowStatus);
        return new RemoteResult(flag);
    }

}


package com.uinnova.product.eam.web.util.bean;

import java.util.Map;

public class DiagramXmlRuleConditions {

	private Long attrId;
	
	private Map<String,Object> ruleOp;
	
	private String value;
	
	private String keyName;
	
	private Integer proType;

	public Long getAttrId() {
		return attrId;
	}

	public void setAttrId(Long attrId) {
		this.attrId = attrId;
	}

	public Map<String, Object> getRuleOp() {
		return ruleOp;
	}

	public void setRuleOp(Map<String, Object> ruleOp) {
		this.ruleOp = ruleOp;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getKeyName() {
		return keyName;
	}

	public void setKeyName(String keyName) {
		this.keyName = keyName;
	}

	public Integer getProType() {
		return proType;
	}

	public void setProType(Integer proType) {
		this.proType = proType;
	}
	
	
	
	
	
}

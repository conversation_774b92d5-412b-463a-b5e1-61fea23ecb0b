package com.uinnova.product.eam.web.flow.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.vo.ESCISearchBeanVO;
import com.uinnova.product.eam.service.FlowFormService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/6/26 下午1:47
 */
@RestController
@RequestMapping("/flowManager/flowForm/")
public class FlowFormController {

    @Resource
    private FlowFormService flowFormService;

    @GetMapping("queryCIClassByCode")
    public RemoteResult queryCIClassByCode(String classCode){
        BinaryUtils.checkEmpty(classCode,"分类标识");
        return new RemoteResult(flowFormService.queryCIClassByCode(classCode));
    }

    @PostMapping("queryFlowFormList")
    public RemoteResult queryFlowFormList(@RequestBody ESCISearchBeanVO bean){
        BinaryUtils.checkEmpty(bean.getClassCodes(),"分类标识");
        return new RemoteResult(flowFormService.queryFlowFormList(bean));
    }
}

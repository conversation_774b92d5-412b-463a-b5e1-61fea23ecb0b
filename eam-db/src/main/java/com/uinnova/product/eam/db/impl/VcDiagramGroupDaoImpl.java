package com.uinnova.product.eam.db.impl;


import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.model.CVcDiagramGroup;
import com.uinnova.product.eam.comm.model.VcDiagramGroup;
import com.uinnova.product.eam.db.VcDiagramGroupDao;
import com.uinnova.product.eam.db.bean.GroupReadCount;
import com.uinnova.product.eam.db.bean.TeamDiagrams;
import com.uinnova.product.eam.db.bean.UserDiagramInfoCount;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 视图组表[VC_DIAGRAM_GROUP]数据访问对象实现
 */
public class VcDiagramGroupDaoImpl extends ComMyBatisBinaryDaoImpl<VcDiagramGroup, CVcDiagramGroup> implements VcDiagramGroupDao {

//	@Override
//	public String getTableName() {
//		return "IAMS." + getDaoDefinition().getTableName();
//	}

	@Override
	public List<UserDiagramInfoCount> countDiagramByDeployUserId(Long domainId, Long startDate, Long endDate) {
		BinaryUtils.checkEmpty(domainId, "domainId");
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("domainId", domainId);
		map.put("startDate", startDate);
		map.put("endDate", endDate);
		return getSqlSession().selectList(getTableName() + ".countDiagramByDeployUserId", map);
	}

	@Override
	public Map<Long, String> selectAllTeamDiagramByUserId(Long userId) {
		BinaryUtils.checkEmpty(userId, "userId");
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("userId", userId);
		List<TeamDiagrams> teamDiagrams = getSqlSession().selectList(getTableName() + ".selectAllTeamDiagramByUserId", map);
		Map<Long, String> result = new HashMap<>();
		for (TeamDiagrams teamDiagram : teamDiagrams) {
			result.put(teamDiagram.getDiagramId(), teamDiagram.getDiagramName());
		}
		return result;
	}

    public List<GroupReadCount> countReadByGroupId(Long domainId, Long startDate, Long endDate) {
        BinaryUtils.checkEmpty(domainId, "domainId");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("domainId", domainId);
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        return getSqlSession().selectList(getTableName() + ".countReadByGroupId", map);
    }
}



package com.uinnova.product.eam.model.cj.domain;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.cj.BaseEntity;
import com.uinnova.product.eam.model.cj.vo.RowTableContentVo;
import lombok.Data;

import java.util.List;

/**
 * @description: 方案模板章节内容
 * @author: Lc
 * @create: 2022-01-05 17:56
 */
@Data
@Comment("方案模板章节内容")
public class PlanTemplateChapterData extends BaseEntity implements Condition {

    @Comment("类型， 1:制品 2:表格 3:富文本 4:清单制品 5:附件")
    private Integer type;
    @Comment("配置顺序")
    private Integer configOrder;
    //@Comment("预览顺序")
    //private Integer previewOrder;
    @Comment("模板章节id")
    private Long templateChapterId;
    @Comment("模板id")
    private Long planTemplateId;

    @Comment("制品名称")
    private String productName;
    //@Comment("制品类型")
    //private String productType;
    @Comment("制品是否必填 0:否 1:是")
    private String productRequired;
    @Comment("视图制品类型")
    private String diagramProductType;
    @Comment("限定视图类型 0:限定类型 1:全部类型")
    private String productControl;

    @Comment("表格名称")
    private String dataTableName;
    @Comment("表格形式 1:横向表格 2:纵向表格 3:矩阵表格")
    private Integer dataTableForm;
    @Comment("表格是否必填 0:非必填   1:必填")
    private String dataTableRequired;
    @Comment("用户自行添加行 0:否 1:是")
    private String userAddRow;
    @Comment("是否配置序号 0:否 1:是")
    private String configSerial;
    @Comment("数据表格内容")
    private List<RowTableContentVo> dataTableContent;
    @Comment("矩阵表格配置，1:横向设置 2:纵向设置")
    private Integer matrixConfig;
    @Comment("矩阵表格行内容")
    private List<RowTableContentVo> matrixRow;
    @Comment("矩阵表格列内容")
    private List<RowTableContentVo> matrixCol;
    @Comment("纵向表格与矩阵表格首列宽 列宽 大 中 小 自定义")
    private Integer firstColumnWidth;
    @Comment("纵向表格与矩阵表格首列宽数值 为空取120")
    private Integer firstColumnWidthValue;
    @Comment("说明 0:不显示 1:显示")
    private String explain;
    @Comment("可编辑说明 1:查看2:编辑")
    private String dataTableRemarkStatus;
    @Comment("备注信息")
    private String dataTableRemarkInfo;


    @Comment("富文本名称")
    private String richTextName;
    @Comment("富文本是否必填 0:非必填   1:必填")
    private String richTextRequired;
    // @Comment("富文本备注 0:否 1:只读")
    // private String richTextRemark;
    @Comment("富文本备注内容")
    private String richTextContent;
    @Comment("富文本初始化数据")
    private String initRichText;

    // 清单制品
    @Comment("清单制品名称")
    private String dataSetName;
    @Comment("清单制品是否必填 0:否   1:是")
    private String dataSetRequired;
    @Comment("清单制品类型")
    private Long dataSetId;

    //附件内容块
    @Comment("附件名称")
    private String appendixName;
    @Comment("附件内容块是否必填 0:否 1:是")
    private String appendixRequired;
    //@Comment("富文本备注 0:不显示 1:显式")
    //private String appendixShow;
    @Comment("附件说明内容")
    private String appendixContent;

    @Comment("参考规范信息")
    private ReferencesInfo referencesInfo;

}

package com.uinnova.product.eam.model.dto;

import com.uinnova.product.eam.comm.model.es.FlowSystemAssociatedFeatures;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/5/27 15:08
 */
@Data
public class FlowSystemAssociatedFeaturesDto extends FlowSystemAssociatedFeatures {

    private CcCi ci;

    private Map<String,String> attrs;

    private List<String> linkedCiCodes;
}

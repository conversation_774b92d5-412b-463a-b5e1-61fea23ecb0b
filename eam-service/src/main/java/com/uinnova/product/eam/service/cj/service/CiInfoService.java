package com.uinnova.product.eam.service.cj.service;

import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCISearchBean;

/**
 * ci service
 *
 * <AUTHOR>
 */
public interface CiInfoService {

    /**
     * 条件查询ci列表
     *
     * @param bean 查询条件
     * @param libType 库类型
     * @return ci列表
     */
    CiGroupPage getCiList(LibType libType, ESCISearchBean bean);

    /**
     * @param bean 查询条件
     * @param libType 库类型
     * @return CI个数
     */
    Integer getCiTotalRows(LibType libType, ESCISearchBean bean);
}

package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.bean.EamFeedback;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

/**
 * 意见反馈数据访问层
 *
 * <AUTHOR>
 */
@Repository
public class IamsESFeedbackDao extends AbstractESBaseDao<EamFeedback, EamFeedback> {
    @Override
    public String getIndex() {
        return "uino_eam_feedback";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}

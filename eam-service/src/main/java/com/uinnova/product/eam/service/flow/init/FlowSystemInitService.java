package com.uinnova.product.eam.service.flow.init;

import cn.hutool.core.io.resource.ClassPathResource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.collect.Sets;
import com.uinnova.product.eam.service.IEamArtifactSvc;
import com.uinnova.product.eam.service.asset.BmConfigSvc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.api.client.cmdb.IVisualModelApiSvc;
import com.uino.bean.cmdb.base.*;
import com.uino.dao.cmdb.ESRltClassSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.cmdb.microservice.IDirSvc;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.service.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025/2/25 14:48
 */
@Slf4j
@Service
public class FlowSystemInitService {

    @Resource
    protected IRltClassSvc iRltClassSvc;

    @Resource
    private ICIClassSvc classSvc;

    @Resource
    private ESRltClassSvc esRltClassSvc;

    @Resource
    private IDirSvc dirSvc;

    @Resource
    private IVisualModelApiSvc iVisualModelApiSvc;

    @Resource
    private BmConfigSvc bmConfigSvc;

    @Resource
    private IEamArtifactSvc iEamArtifactSvc;

    @Value("${uino.eam.flow.init.class:LogicEntity,PhysicalEntity,DataType,StandardClass,Standard,Attributes" +
            ",DomainClass,DM_BUS_OBJ,ConceptionEntity,Domain,成熟度评价,业务建模全景图,要素,流程组,流程,制度,标准大类,标准,场景,风险,术语,岗位角色" +
            ",应用系统,应用模块,应用服务,应用域,KCP,表单,主题域,二级主题域,业务对象,活动,指标,部门,岗位,开始事件,结束事件,中间事件,排他网关" +
            ",包容网关,并行网关,端到端流程,价值流,价值流阶段,前置流程,后置流程,档案,分级,应用组件,机房,网络分区,设备节点,部署单元,系统软件}")
    private String classCodesStr;

    @Value("${uino.eam.flow.init.rlt:DM-包含,位于,部署于,包含,衍生,引用,关联,参与,顺序流,拥有,输入,输出,KCP-包含,使用,属于,由...执行,由…实现}")
    private String rltClassNameStr;


    public ResponseEntity<byte[]> getFlowInitData() {
        //1、获取流程体系需要的关系定义数据，并导出json，这里关系可以通过名称新建出来，不用导出
        //2、获取流程体系需要的分类定义数据，并导出json
        List<CcCiClassInfo> allCiClass = classSvc.queryClassByCdt(new CCcCiClass());
        HashMap<Long, CcCiClassInfo> allCiClassMap = new HashMap<>();
        for (CcCiClassInfo ciClass : allCiClass) {
            allCiClassMap.put(ciClass.getCiClass().getId(), ciClass);
        }
        HashMap<Long, CcCiClassInfo> allRltClassMap = new HashMap<>();
        List<CcCiClassInfo> allRltClass = iRltClassSvc.queryAllClasses(1L);
        for (CcCiClassInfo ciClass : allRltClass) {
            allRltClassMap.put(ciClass.getCiClass().getId(), ciClass);
        }
        String[] classCodes = classCodesStr.split(",");
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(classCodes);
        List<CcCiClassInfo> ccCiClassInfos = classSvc.queryClassByCdt(cCcCiClass);
        for (CcCiClassInfo ccCiClassInfo : ccCiClassInfos) {
            List<CcCiAttrDef> attrDefs = ccCiClassInfo.getAttrDefs();
            for (CcCiAttrDef attrDef : attrDefs) {
                if (attrDef.getProType() == ESPropertyType.LINK_CI.getValue()) {
                    //关联资产做下处理,设置关联资产的字段转换成分类code，如果关联资产设置关系构建，找到对应的关系并用分号隔开存储
                    String proDropSourceDef = attrDef.getProDropSourceDef();
                    Long constructRltId = attrDef.getConstructRltId();
                    String sourceOrTarget = attrDef.getSourceOrTarget();
                    if (StringUtils.isNoneBlank(proDropSourceDef)) {
                        if (StringUtils.isNotBlank(sourceOrTarget) && constructRltId != null) {
                            //记录ci和关系构建数据
                            CcCiClassInfo rltClassInfo = allRltClassMap.get(constructRltId);
                            CcCiClassInfo ccCiClassInfo1 = allCiClassMap.get(Long.parseLong(proDropSourceDef));
                            if (rltClassInfo != null && ccCiClassInfo1 != null) {
                                String rltClassCode = rltClassInfo.getCiClass().getClassCode();
                                String classCode = ccCiClassInfo1.getCiClass().getClassCode();
                                attrDef.setProDropSourceDef(classCode + ":" + rltClassCode);
                            }
                        } else {
                            //只转换ci数据
                            CcCiClassInfo ccCiClassInfo1 = allCiClassMap.get(Long.parseLong(proDropSourceDef));
                            if (ccCiClassInfo1 != null) {
                                attrDef.setProDropSourceDef(ccCiClassInfo1.getCiClass().getClassCode());
                            }
                        }
                    }
                }else if (attrDef.getProType() == ESPropertyType.EXTERNAL_ATTR.getValue()) {
                    //将关联属性的id转换成名称
                    String proDropSourceDef = attrDef.getProDropSourceDef();
                    //截取第1位到倒数第2位
                    String substring = proDropSourceDef.substring(1, proDropSourceDef.length() - 1);
                    String[] split = substring.split(":");
                    String classId =  split[0];
                    String classAttrId =  split[1];
                    String className;
                    String attrName="";
                    CcCiClassInfo ccCiClassInfo1 = allCiClassMap.get(Long.parseLong(classId));
                    className = ccCiClassInfo1.getCiClass().getClassCode();
                    List<CcCiAttrDef> attrDefs1 = ccCiClassInfo1.getAttrDefs();
                    for (CcCiAttrDef ccCiAttrDef : attrDefs1) {
                        if(ccCiAttrDef.getId().equals(Long.parseLong(classAttrId))){
                            attrName = ccCiAttrDef.getProStdName();
                        }
                    }
                    attrDef.setProDropSourceDef(className + ":" + attrName);
                }
            }
        }

        // 创建 ObjectMapper 实例
        ObjectMapper objectMapper = new ObjectMapper();
        // 可选：配置 ObjectMapper 以美化输出（格式化 JSON）
        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
        // 定义输出文件路径
        String filename = "uino_flow_system_class.json";
        Path filePath = Paths.get(filename);
        String jsonData = "";
        byte[] fileContent;
        try {
            jsonData = objectMapper.writeValueAsString(ccCiClassInfos);
            try (OutputStream outputStream = Files.newOutputStream(filePath)) {
                byte[] data = jsonData.getBytes(StandardCharsets.UTF_8);
                outputStream.write(data); // 将 JSON 数据写入文件
            }
            fileContent = Files.readAllBytes(filePath);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);  // 设置 Content-Type 为 application/json
        headers.setContentDispositionFormData("attachment", filename);  // 设置 Content-Disposition 头

        try {
            Files.deleteIfExists(filePath);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return new ResponseEntity<>(fileContent, headers, HttpStatus.OK);
        //3、获取流程体系需要的元模型数据，并导出json
        //手动导出即可
        //4、设置流程体系权限数据
        //手动配置
    }


    public void initFlowData() {
        //1、获取流程体系需要的关系定义数据，新建不存在的关系
        log.info("新建流程体系所需要关系");
        String[] rltClassName = rltClassNameStr.split(",");
        HashSet<String> rltClassNameSet = new HashSet<>(Arrays.asList(rltClassName));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", 1L));
        query.must(QueryBuilders.termsQuery("className.keyword", rltClassName));
        List<ESCIClassInfo> listByQuery = esRltClassSvc.getListByQuery(query);
        HashSet<String> exitClassNameSet = new HashSet<>();
        for (ESCIClassInfo esciClassInfo : listByQuery) {
            exitClassNameSet.add(esciClassInfo.getClassName());
        }
        //获取rltClassNameSet和exitClassNameSet差集
        Sets.SetView<String> difference = Sets.difference(rltClassNameSet, exitClassNameSet);
        ArrayList<ESCIClassInfo> esciClassInfos = new ArrayList<>();
        for (String s : difference) {
            ESCIClassInfo esciClassInfo = new ESCIClassInfo();
            esciClassInfo.setAttrDefs(new ArrayList<>());
            esciClassInfo.setClassName(s);
            esciClassInfo.setClassCode(s);
            esciClassInfo.setLineBorder(1);
            esciClassInfo.setLineColor("#000");
            esciClassInfo.setLineDirect("none_Standard-fill");
            esciClassInfo.setLineDispType(1);
            esciClassInfo.setLineEaStyle("default");
            esciClassInfo.setLineType("solid");
            esciClassInfo.setParentId(0L);
            esciClassInfos.add(esciClassInfo);
        }
        esRltClassSvc.saveOrUpdateBatch(esciClassInfos);
        //2、获取流程体系需要的分类定义数据，判断分类是否存在，不存在新建
        String[] classCodes = classCodesStr.split(",");
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(classCodes);
        List<CcCiClassInfo> ccCiClassInfos = classSvc.queryClassByCdt(cCcCiClass);
        HashSet<String> systemExitClassCode = new HashSet<>();
        for (CcCiClassInfo ccCiClassInfo : ccCiClassInfos) {
            systemExitClassCode.add(ccCiClassInfo.getCiClass().getClassCode());
        }
        log.info("导入流程体系相关分类");
        //读取初始化文件判断分类是否存在，不存在新建分类
        List<JSONObject> jsonData = FileUtil.getData("/initdata/uino_flow_system_class.json",
                JSONObject.class);
        List<CcCiClassInfo> datas = new ArrayList<>();
        //因要保存tag所以需要自己解析
        for (JSONObject jsonDatum : jsonData) {
            CcCiClassInfo ccCiClassInfo = new CcCiClassInfo();
            CcCiClass ciClass = jsonDatum.getObject("ciClass", CcCiClass.class);
            ccCiClassInfo.setCiClass(ciClass);
            List<CcCiAttrDef> attrDefs = new ArrayList<>();
            JSONArray jsonArray = jsonDatum.getJSONArray("attrDefs");
            for (int i = 0; i < jsonArray.size(); i++) {
                ESCIAttrDefInfo object = jsonArray.getObject(i, ESCIAttrDefInfo.class);
                attrDefs.add(object);
            }
            ccCiClassInfo.setAttrDefs(attrDefs);
            datas.add(ccCiClassInfo);
        }

        datas.removeIf(next -> systemExitClassCode.contains(next.getCiClass().getClassCode()));

        //datas中属性是关联资产的要把对应的id做下转换处理
        CCcCiClassDir cCcCiClassDir = new CCcCiClassDir();
        cCcCiClassDir.setDirName("流程体系");
        List<CcCiClassDir> ccCiClassDirs = dirSvc.queryDirByDirName(cCcCiClassDir);
        Long domainDirId = null;
        if (CollectionUtils.isEmpty(ccCiClassDirs)) {
            //创建流程体系领域
            CcCiClassDir dir = new CcCiClassDir();
            dir.setIsLeaf(1);
            if (dir.getDirLvl() == null) {
                dir.setDirLvl(1);
            }
            dir.setDirName("流程体系");
            dir.setCiType(1);
            dir.setParentId(0L);
            dir.setDirPath("#" + ESUtil.getUUID() + "#");
            dir.setDomainId(1L);
            domainDirId = dirSvc.saveOrUpdate(dir);
        } else {
            domainDirId = ccCiClassDirs.get(0).getId();
        }

        //判断是否存在架构设计工具系统核心配置领域
        Long bmDomainDirId = null;
        CCcCiClassDir bmConfigCiClassDir = new CCcCiClassDir();
        bmConfigCiClassDir.setDirName("架构设计工具系统核心配置");
        List<CcCiClassDir> bmCiClassDirs = dirSvc.queryDirByDirName(bmConfigCiClassDir);
        if (CollectionUtils.isEmpty(bmCiClassDirs)) {
            //创建架构设计工具系统核心配置领域
            CcCiClassDir dir = new CcCiClassDir();
            dir.setIsLeaf(1);
            if (dir.getDirLvl() == null) {
                dir.setDirLvl(1);
            }
            dir.setDirName("架构设计工具系统核心配置");
            dir.setCiType(1);
            dir.setParentId(0L);
            dir.setDirPath("#" + ESUtil.getUUID() + "#");
            dir.setDomainId(1L);
            bmDomainDirId = dirSvc.saveOrUpdate(dir);
        } else {
            bmDomainDirId = bmCiClassDirs.get(0).getId();
        }

        //判断是否存在成熟度领域
        Long maturityDomainDirId = null;
        CCcCiClassDir maturityConfigCiClassDir = new CCcCiClassDir();
        maturityConfigCiClassDir.setDirName("成熟度");
        List<CcCiClassDir> maturityCiClassDirs = dirSvc.queryDirByDirName(maturityConfigCiClassDir);
        if (CollectionUtils.isEmpty(maturityCiClassDirs)) {
            //创建架构设计工具系统核心配置领域
            CcCiClassDir dir = new CcCiClassDir();
            dir.setIsLeaf(1);
            if (dir.getDirLvl() == null) {
                dir.setDirLvl(1);
            }
            dir.setDirName("成熟度");
            dir.setCiType(1);
            dir.setParentId(0L);
            dir.setDirPath("#" + ESUtil.getUUID() + "#");
            dir.setDomainId(1L);
            maturityDomainDirId = dirSvc.saveOrUpdate(dir);
        } else {
            maturityDomainDirId = bmCiClassDirs.get(0).getId();
        }

        //判断是否存在数据建模领域
        Long dataModelDomainDirId = null;
        CCcCiClassDir dataModelConfigCiClassDir = new CCcCiClassDir();
        dataModelConfigCiClassDir.setDirName("S-F.B.DA");
        List<CcCiClassDir> dataModelCiClassDirs = dirSvc.queryDirByDirName(dataModelConfigCiClassDir);
        if (CollectionUtils.isEmpty(dataModelCiClassDirs)) {
            //创建架构设计工具系统核心配置领域
            CcCiClassDir dir = new CcCiClassDir();
            dir.setIsLeaf(1);
            if (dir.getDirLvl() == null) {
                dir.setDirLvl(1);
            }
            dir.setDirName("S-F.B.DA");
            dir.setCiType(1);
            dir.setParentId(0L);
            dir.setDirPath("#" + ESUtil.getUUID() + "#");
            dir.setDomainId(1L);
            dataModelDomainDirId = dirSvc.saveOrUpdate(dir);
        } else {
            dataModelDomainDirId = bmCiClassDirs.get(0).getId();
        }

        //LogicEntity,PhysicalEntity,DataType,StandardClass,Standard,Attributes,DomainClass,DM_BUS_OBJ
        Set<String> dataSetClass = new HashSet<>();
        dataSetClass.add("LogicEntity");
        dataSetClass.add("PhysicalEntity");
        dataSetClass.add("DataType");
        dataSetClass.add("StandardClass");
        dataSetClass.add("Standard");
        dataSetClass.add("Attributes");
        dataSetClass.add("DomainClass");
        dataSetClass.add("DM_BUS_OBJ");
        dataSetClass.add("ConceptionEntity");
        dataSetClass.add("Domain");

        //获取所有分类数据和关系数据
        List<CcCiClassInfo> allCiClass = classSvc.queryClassByCdt(new CCcCiClass());
        HashMap<String, Long> allCiClassMap = new HashMap<>();
        HashMap<String, CcCiClassInfo> allCiClassInfoMap = new HashMap<>();
        for (CcCiClassInfo ciClass : allCiClass) {
            allCiClassMap.put(ciClass.getCiClass().getClassCode(), ciClass.getCiClass().getId());
            allCiClassInfoMap.put(ciClass.getCiClass().getClassCode(), ciClass);
        }
        HashMap<String, Long> allRltClassMap = new HashMap<>();
        List<CcCiClassInfo> allRltClass = iRltClassSvc.queryAllClasses(1L);
        for (CcCiClassInfo ciClass : allRltClass) {
            allRltClassMap.put(ciClass.getCiClass().getClassName(), ciClass.getCiClass().getId());
        }
        //先重置下所有的id
        Iterator<CcCiClassInfo> iterator = datas.iterator();
        while (iterator.hasNext()) {
            //已经存在的分类就不在新增
            CcCiClassInfo data = iterator.next();
            String classCode = data.getCiClass().getClassCode();
            if (allCiClassMap.containsKey(classCode)) {
                iterator.remove();
            }
            CcCiClass ciClass = data.getCiClass();
            ciClass.setId(ESUtil.getUUID());
            if ("视图配置".equals(data.getCiClass().getClassName())) {
                ciClass.setDirId(bmDomainDirId);
            } else if ("成熟度评价".equals(data.getCiClass().getClassName())) {
                ciClass.setDirId(maturityDomainDirId);
            } else {
                ciClass.setDirId(domainDirId);
            }
            if(dataSetClass.contains(ciClass.getClassCode())){
                ciClass.setDirId(dataModelDomainDirId);
            }
            allCiClassMap.put(ciClass.getClassCode(), ciClass.getId());
            allCiClassInfoMap.put(ciClass.getClassCode(), data);
            List<CcCiAttrDef> attrDefs = data.getAttrDefs();
            for (CcCiAttrDef attrDef : attrDefs) {
                attrDef.setId(ESUtil.getUUID());
                attrDef.setClassId(ciClass.getId());
            }
        }
        //关联资产查询配置map配置正确的关系id和ciClassId
        List<CcCiClassInfo> nextSaveList = new ArrayList<>();
        Iterator<CcCiClassInfo> iterator1 = datas.iterator();
        while (iterator1.hasNext()) {
            CcCiClassInfo data = iterator1.next();
            List<CcCiAttrDef> attrDefs = data.getAttrDefs();
            boolean nextCi = false;
            for (CcCiAttrDef attrDef : attrDefs) {
                if (attrDef.getProType() == ESPropertyType.LINK_CI.getValue()) {
                    String proDropSourceDef = attrDef.getProDropSourceDef();
                    if (proDropSourceDef.contains(":")) {
                        String[] split = proDropSourceDef.split(":");
                        String classCode = split[0];
                        Long ciClassId = allCiClassMap.get(classCode);
                        String rltName = split[1];
                        Long rltClassId = allRltClassMap.get(rltName);
                        attrDef.setProDropSourceDef(ciClassId.toString());
                        attrDef.setConstructRltId(rltClassId);
                    } else {
                        Long ciClassId = allCiClassMap.get(proDropSourceDef);
                        attrDef.setProDropSourceDef(ciClassId.toString());
                    }
                }else if(attrDef.getProType() == ESPropertyType.EXTERNAL_ATTR.getValue()){
                    //关联属性将关联的分类和属性设置到配置中
                    String proDropSourceDef = attrDef.getProDropSourceDef();
                    String[] split = proDropSourceDef.split(":");
                    String classCode = split[0];
                    String attrName = split[1];
                    CcCiClassInfo ccCiClassInfo = allCiClassInfoMap.get(classCode);
                    for (CcCiAttrDef def : ccCiClassInfo.getAttrDefs()) {
                        if(def.getProStdName().equalsIgnoreCase(attrName)){
                            attrDef.setProDropSourceDef("{"+ccCiClassInfo.getCiClass().getId()+":"+def.getId()+"}");
                            nextCi = true;
                        }
                    }
                }
            }
            if(nextCi){
                //流程属性需要第二步保存才行
                nextSaveList.add(data);
                iterator1.remove();
            }
        }
        if (!CollectionUtils.isEmpty(datas)) {
            classSvc.saveOrUpdateBatch(1L, datas);
        }
        if (!CollectionUtils.isEmpty(nextSaveList)) {
            classSvc.saveOrUpdateBatch(1L, nextSaveList);
        }
        //3、获取流程体系需要的元模型数据，导入元模型
        log.info("导入元模型");
        JSONObject flowSystemMeteData = FileUtil.readObject("/initdata/uino_flow_system_meta_model.json",
                JSONObject.class);
        JSONArray nodeDataArray = flowSystemMeteData.getJSONArray("nodeDataArray");
        JSONArray linkDataArray = flowSystemMeteData.getJSONArray("linkDataArray");
        for (int i = 0; i < nodeDataArray.size(); i++) {
            JSONObject jsonObject = nodeDataArray.getJSONObject(i);
            String classCode = jsonObject.getString("classCode");
            Long classId = allCiClassMap.get(classCode);
            if (classId != null) {
                jsonObject.put("classId", classId);
            }
        }

        for (int i = 0; i < linkDataArray.size(); i++) {
            JSONObject jsonObject = linkDataArray.getJSONObject(i);
            String classCode = jsonObject.getString("classCode");
            Long classId = allRltClassMap.get(classCode);
            if (classId != null) {
                jsonObject.put("classId", classId);
            }
        }

        List<ESVisualModel> esVisualModels = iVisualModelApiSvc.queryVisualModels(1L);
        ESVisualModel currentVisualModel = null;
        if (CollectionUtils.isEmpty(esVisualModels)) {
            currentVisualModel = new ESVisualModel();
            currentVisualModel.setDomainId(1L);
            currentVisualModel.setEnable(true);
            currentVisualModel.setJson("[]");
            currentVisualModel.setName("在用元模型");
        } else {
            for (ESVisualModel esVisualModel : esVisualModels) {
                if (esVisualModel.getEnable()) {
                    currentVisualModel = esVisualModel;
                }
            }
        }
        String json = currentVisualModel.getJson();
        JSONArray objects = JSON.parseArray(json);
        boolean addMetaVisual = false;
        for (int i = 0; i < objects.size(); i++) {
            String name = objects.getJSONObject(i).getString("name");
            if ("流程体系".equalsIgnoreCase(name)) {
                addMetaVisual = true;
            }
        }
        if (!addMetaVisual) {
            flowSystemMeteData.put("active", true);
            flowSystemMeteData.put("sheetId", ESUtil.getUUID());
            objects.add(flowSystemMeteData);
            currentVisualModel.setJson(JSON.toJSONString(objects));
            iVisualModelApiSvc.saveVisualModel(currentVisualModel);
        }
        //4、导入制品
        String flowDiagramArtifactConfig = bmConfigSvc.getConfigType("FLOW_DIAGRAM_ARTIFACT_CONFIG");
        JSONObject jsonObject;
        if (StringUtils.isNotBlank(flowDiagramArtifactConfig)) {
            jsonObject = JSON.parseObject(flowDiagramArtifactConfig);
        } else {
            jsonObject = new JSONObject();
        }
        //流程图
        ClassPathResource flowSystemArtifact = new ClassPathResource("/initdata/flow_system_artifact.zip");
        try {
            Long artifactId = iEamArtifactSvc.importArtifact(flowSystemArtifact.getFile());
            if (artifactId != null) {
                jsonObject.put("流程图", artifactId);
            }
        } catch (Exception e) {
            log.error("流程体系流程图导入异常:", e);
        }
        //端到端流程图
        ClassPathResource c2cArtifactZip = new ClassPathResource("/initdata/c2c_flow_artifact.zip");
        try {
            Long artifactId = iEamArtifactSvc.importArtifact(c2cArtifactZip.getFile());
            if (artifactId != null) {
                jsonObject.put("端到端流程图", artifactId);
            }
        } catch (Exception e) {
            log.error("流程体系端到端流程导入异常:", e);
        }
        //集成关系图
        ClassPathResource integrationRelationArtifactZip = new ClassPathResource("/initdata/flow_system_Integration_relation.zip");
        try {
            Long artifactId = iEamArtifactSvc.importArtifact(integrationRelationArtifactZip.getFile());
            if (artifactId != null) {
                jsonObject.put("集成关系图", artifactId);
            }
        } catch (Exception e) {
            log.error("流程体系端到端流程导入异常:", e);
        }
        bmConfigSvc.saveOrUpdateConfType("FLOW_DIAGRAM_ARTIFACT_CONFIG", JSON.toJSONString(jsonObject), "流程架构视图绑定制品id");
        //设置其他配系统配置init_system_config.json
        JSONArray systemConfigJsonArr = FileUtil.readObject("/initdata/init_system_config.json", JSONArray.class);
        systemConfigJsonArr.forEach(next -> {
            JSONObject jsonObject1 = (JSONObject) next;
            String confType = jsonObject1.getString("CONF_TYPE");
            String configType = bmConfigSvc.getConfigType(confType);
            if(StringUtils.isBlank(configType)){
                String confJson = jsonObject1.getString("CONF_JSON");
                String confName = jsonObject1.getString("CONF_NAME");
                bmConfigSvc.saveOrUpdateConfType(confType, confJson, confName);
            }
        });

        //5、设置流程体系权限数据
    }




}

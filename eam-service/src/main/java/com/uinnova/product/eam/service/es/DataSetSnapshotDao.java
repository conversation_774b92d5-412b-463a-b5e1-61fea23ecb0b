package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.DataSetSnapshot;
import com.uino.dao.AbstractESBaseDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
@Slf4j
public class DataSetSnapshotDao extends AbstractESBaseDao<DataSetSnapshot, DataSetSnapshot> {
    @Override
    public String getIndex() {
        return "uino_eam_dataset_snapshot";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

}

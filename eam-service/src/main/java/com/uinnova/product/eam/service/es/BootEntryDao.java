package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.BootEntryConfig;
import com.uinnova.product.eam.comm.model.es.BootEntryUserConfig;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.List;

@Service
public class BootEntryDao  extends AbstractESBaseDao<BootEntryConfig, BootEntryConfig> {
    @Override
    public String getIndex() {
        return "uino_eam_boot_entry_config";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        List<BootEntryConfig> list = FileUtil.getData("/initdata/uino_eam_boot_entry_config.json", BootEntryConfig.class);
        super.initIndex(1);
        deleteByQuery(QueryBuilders.matchAllQuery(),Boolean.TRUE);
        saveOrUpdateBatch(list);
    }
}

package com.uinnova.product.eam.service.utils;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.model.cj.enums.FormulaTypeEnum;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.util.PropertyType;
import com.uino.bean.cmdb.base.ESCIAttrTransConfig;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

/**
 * 计算工具类
 * <AUTHOR>
 */
public class CalculateUtil {

    public static BigDecimal calculate(String formula, Map<String, Object> attrs, String formulaType, Map<String, CcCiAttrDef> defMap, Map<Long, ESCIAttrTransConfig> attrConfigMap) {

        if(BinaryUtils.isEmpty(formula)){
            return BigDecimal.valueOf(0).setScale(2);
        }
        for (Map.Entry<String, Object> entry : attrs.entrySet()) {
            PropertyType type = null;
            CcCiAttrDef def = defMap.get(entry.getKey());
            if (!BinaryUtils.isEmpty(def)) {
                Integer proType = def.getProType();
                ESCIAttrTransConfig transConfig = attrConfigMap.get(def.getId());
                if (transConfig != null && transConfig.getTargetAttrType() != null) {
                    proType = transConfig.getTargetAttrType();
                }
                type = PropertyType.valueOf(proType);
            }
            String value = String.valueOf(entry.getValue());
            try {
                if(value.contains("%")){
                    value = value.replace("%","").trim();
                    value = String.valueOf(Double.parseDouble(value) / 100);
                }
                if(PropertyType.PERCENT.equals(type)){
                    value = String.valueOf(Double.parseDouble(value) / 100);
                }
                Double.parseDouble(value);
            } catch (NumberFormatException e) {
                value = "0";
            }
            if(FormulaTypeEnum.CALCULATE.getType().equals(formulaType)){
                //计算属性根据配置约束
                formula = formula.replace("${" + entry.getKey() + "}", value);
            }

        }

        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("js");

        try {
            Object result = engine.eval(formula);
            BigDecimal calculatedResult = new BigDecimal(result.toString());
            return calculatedResult.setScale(2, RoundingMode.HALF_UP);
        } catch (ScriptException e) {
//            e.printStackTrace();
        }
        return BigDecimal.valueOf(0).setScale(2);
    }

    public static Boolean judge(String expression) {
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("js");

        try {
            Object result = engine.eval(expression);
            return (Boolean) result;
        } catch (ScriptException e) {
            e.printStackTrace();
        }
        return false;
    }
}

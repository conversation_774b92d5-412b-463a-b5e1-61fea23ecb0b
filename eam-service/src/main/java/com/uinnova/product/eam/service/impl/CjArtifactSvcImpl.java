package com.uinnova.product.eam.service.impl;

import com.uinnova.product.eam.service.es.EamArtifactDao;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.util.CollectionUtils;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.model.FileResourceMeta;
import com.uinnova.product.eam.comm.model.es.EamArtifact;
import com.uinnova.product.eam.model.dto.ElementConditionDto;
import com.uinnova.product.eam.model.enums.ArtifactEnum;
import com.uinnova.product.eam.model.vo.CjArtifactVo;
import com.uinnova.product.eam.service.CjArtifactSvc;
import com.uinnova.product.eam.service.IEamArtifactSvc;
import com.uinnova.product.eam.service.resource.IEamResourceSvc;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 仓颉制品分类
 * @author: Lc
 * @create: 2022-08-31 16:00
 */
@Service
public class CjArtifactSvcImpl implements CjArtifactSvc {

    private static final String DEFAULT_DIAGRAM = "/defaultDiagram/freeDiagram.png";

    @Resource
    private IEamArtifactSvc iEamArtifactSvc;

    @Resource
    private EamArtifactDao eamArtifactDao;

    @Resource
    private IEamResourceSvc eamResourceSvc;

    @Value("${http.resource.space}")
    private String httpResouceUrl;

    @Override
    public List<CjArtifactVo> findArtifactList(ElementConditionDto dto) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("dataStatus", 1));
        boolQueryBuilder.must(QueryBuilders.termQuery("releaseState", 1));

        if (!BinaryUtils.isEmpty(dto) && !BinaryUtils.isEmpty(dto.getName())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("artifactName.keyword", dto.getName()));
        }
        List<EamArtifact> eamArtifactList = eamArtifactDao.getListByQuery(boolQueryBuilder);
        Map<Integer, List<EamArtifact>> eamArtifactMap = eamArtifactList.stream().collect(Collectors.groupingBy(EamArtifact::getTypeClassification));

        // 添加所有制品分类
        List<CjArtifactVo> artifactList = new ArrayList<>();
        // 添加自由视图分类
        CjArtifactVo cjArtifactVo = new CjArtifactVo();
        cjArtifactVo.setId(ArtifactEnum.FREEDOM_DIAGRAM.getArtifactType());
        cjArtifactVo.setType(ArtifactEnum.FREEDOM_DIAGRAM.getArtifactDesc());
        // 配置自由视图数据
        List<EamArtifact> freedomDiagramList = new ArrayList<>();
        EamArtifact freeArtifact = new EamArtifact();
        freeArtifact.setId(Long.valueOf(ArtifactEnum.FREEDOM_DIAGRAM.getArtifactType()));
        freeArtifact.setArtifactName(ArtifactEnum.FREEDOM_DIAGRAM.getArtifactDesc());
        freedomDiagramList.add(freeArtifact);
        cjArtifactVo.setEamArtifactList(freedomDiagramList);
        if (!BinaryUtils.isEmpty(dto) && !BinaryUtils.isEmpty(dto.getName())) {
            if (ArtifactEnum.FREEDOM_DIAGRAM.getArtifactDesc().contains(dto.getName())) {
                artifactList.add(cjArtifactVo);
            }
        } else {
            artifactList.add(cjArtifactVo);
        }

        for(Map.Entry<Integer, List<EamArtifact>> map : eamArtifactMap.entrySet()) {
            Map<String,String> artifactTypeDictMap = iEamArtifactSvc.getArtifactTypeDictMap();
            String artifactTypeInfo = artifactTypeDictMap.get(map.getKey().toString());
            if (artifactTypeInfo != null) {
                CjArtifactVo cjArtifactInfoVo = new CjArtifactVo();
                cjArtifactInfoVo.setId(map.getKey());
                cjArtifactInfoVo.setType(artifactTypeInfo);
                cjArtifactInfoVo.setEamArtifactList(map.getValue());
                artifactList.add(cjArtifactInfoVo);
            }
        }
        return artifactList;
    }

    @Override
    public List<EamArtifact> findAllArtifactList() {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("dataStatus", 1));
        boolQueryBuilder.must(QueryBuilders.termQuery("releaseState", 1));
        List<EamArtifact> eamArtifactList = eamArtifactDao.getListByQuery(boolQueryBuilder);
        if (CollectionUtils.isEmpty(eamArtifactList)) {
            eamArtifactList = new ArrayList<>(1);
        }
        EamArtifact freeArtifact = new EamArtifact();
        freeArtifact.setId(Long.valueOf(ArtifactEnum.FREEDOM_DIAGRAM.getArtifactType()));
        freeArtifact.setArtifactName(ArtifactEnum.FREEDOM_DIAGRAM.getArtifactDesc());
        eamArtifactList.add(freeArtifact);
        return eamArtifactList;
    }

    @Override
    public Map<Long, String> findArtifactListByIds(List<Long> ids) {
        List<EamArtifact> eamArtifactList = iEamArtifactSvc.queryArtifactListByIds(ids, 1);
        Map<Long, String> artifactMap = null;
        if (!CollectionUtils.isEmpty(eamArtifactList)) {
            artifactMap = new HashMap<>();
            for (EamArtifact eamArtifact : eamArtifactList) {
                List<Long> fileIds = eamArtifact.getFileIds();
                if (!StringUtils.isEmpty(fileIds) && fileIds.size() > 0) {
                    List<FileResourceMeta> download = eamResourceSvc.download(fileIds);
                    if (!BinaryUtils.isEmpty(download)) {
                        artifactMap.put(eamArtifact.getId(), download.get(0).getResPath());
                    }
                }
            }
        }
        if (ids.contains(0L)) {
            if (artifactMap == null) {
                artifactMap = new HashMap<>();
            }
            artifactMap.put(0L, httpResouceUrl + DEFAULT_DIAGRAM);
        }
        return artifactMap;
    }

}

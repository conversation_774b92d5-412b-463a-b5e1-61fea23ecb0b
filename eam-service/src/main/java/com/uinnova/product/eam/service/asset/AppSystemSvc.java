package com.uinnova.product.eam.service.asset;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.model.cj.dto.PlanDesignInstanceDTO;
import com.uinnova.product.eam.model.vo.*;
import com.uinnova.product.eam.model.VcCiClassInfoDto;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 *  应用系统接口
 */
public interface AppSystemSvc {

    /**
     *  分页查询应用系统列表
     * @param queryVo
     */
    Page<ESCIInfo> queryAppSystemList(AppSystemQueryVo queryVo);

    /**
     *  根据ciCode获取应用系统数据
     * @param ciCode
     */
    CcCiInfo queryAppSystemByCode(String ciCode);

    /**
     *  获取应用系统的对象定义
     * @return
     */
    ESCIClassInfo getAppSystemClassInfo();

    /**
     *  获取应用系统卡片配置
     * @return
     */
    String getCardConfiguration();

    /**
     *  保存卡片配置
     * @param data
     */
    void saveCardConfiguration(String data);

    /**
     *  根据classId获取上下文
     * @param classId
     * @return
     */
    VcCiClassInfoDto queryAppSystemClassInfo(Long classId, String like);


    /**
     * 获取应用系统列表模式左侧筛选条件
     * @return
     */
    List<AppSystemQueryConVo> getQueryConditions();

    /**
     * 应用架构全景显示配置
     * @return
     */
    AppArchWallConfigVo getAppArchWallConfig();

    /**
     * 通过设计库系统ids获取应用系统列表
     * @param ids
     * @return
     */
    List<ESCIInfo> getByIds(List<String> ids);  /**
     * 通过设计库系统ids获取应用系统列表
     * @param ids
     * @return
     */
    List<ESCIInfo> getPrivateByIds(List<String> ids);

    /**
     * 查询应用系统关联资产
     * @param ciCode
     * @return
     */
    List<AppSystemAssetsVo> getSystemAssets(String ciCode);

    /**
     * 查询应用系统关联架构方案
     * @param ciCode 系统ciCode
     * @return 方案
     */
    Page<PlanDesignInstanceDTO> getSystemPlan(Integer pageNum, Integer pageSize,String ciCode);


    AppSysQueryVo queryAppSystemListNew(AppSystemQueryVo queryVo, LibType libType);

    List<ESCIAttrDefInfo> getDefInfoByClassCode(String classCode);

    ResponseEntity<byte[]> exportBySearch(AppSystemQueryVo queryVo, LibType libType);
}
